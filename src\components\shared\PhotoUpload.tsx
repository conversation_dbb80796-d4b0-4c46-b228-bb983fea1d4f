'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Camera, X } from 'lucide-react';
import Image from 'next/image';
import {
  convertImageToWebp,
  validateFileSize,
  validateImageType,
} from '@/lib/utils/image-utils';
import {
  uploadGymCoverImage,
  uploadTempGymCoverImage,
} from '@/lib/actions/dashboard/company/gym-actions';

interface PhotoUploadProps {
  title?: string;
  description?: string;
  currentCoverUrl?: string | null;
  disabled?: boolean;
  showCurrentImages?: boolean;
  gymId?: string; // varsa settings mode, yoksa setup mode
  onUploadSuccess?: (url: string) => void;
  onUploadError?: (error: string) => void;
}

export function PhotoUpload({
  title = 'Kapak Görseli Yükleme',
  description = 'Salonunuzun kapak görselini yükleyin (isteğe bağlı)',
  currentCoverUrl,
  disabled = false,
  showCurrentImages = false,
  gymId,
  onUploadSuccess,
  onUploadError,
}: PhotoUploadProps) {
  const [coverError, setCoverError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [coverPreview, setCoverPreview] = useState<string | null>(null);

  const validateFile = (file: File): boolean => {
    if (!validateImageType(file)) {
      return false;
    }
    if (!validateFileSize(file, 5)) {
      return false;
    }
    return true;
  };

  // Photo upload işlemini gerçekleştir
  const handleFileUpload = async (file: File) => {
    setUploading(true);

    try {
      // WebP formatına dönüştür (eğer zaten WebP değilse)
      let fileToUpload = file;
      if (file.type !== 'image/webp') {
        try {
          fileToUpload = await convertImageToWebp(file);
          if (gymId) {
            toast.info('Görsel WebP formatına dönüştürülüyor...');
          }
        } catch (conversionError) {
          console.warn(
            'WebP dönüştürme başarısız, orijinal dosya kullanılacak:',
            conversionError
          );
        }
      }

      // gymId varsa settings mode (direct upload), yoksa setup mode (temp upload)
      let result;
      if (gymId) {
        result = await uploadGymCoverImage(fileToUpload, gymId);
      } else {
        result = await uploadTempGymCoverImage(fileToUpload);
      }

      if (result.success && result.url) {
        // Başarılı upload
        if (gymId) {
          toast.success('Kapak görseli başarıyla yüklendi');
          // Preview'ı temizle
          setCoverPreview(null);
          // Sayfayı yenile
          window.location.reload();
        }

        // Callback'i çağır
        if (onUploadSuccess) {
          onUploadSuccess(result.url);
        }
      } else {
        const errorMessage = result.error || 'Görsel yüklenirken hata oluştu';
        if (gymId) {
          toast.error(errorMessage);
        }
        if (onUploadError) {
          onUploadError(errorMessage);
        }
      }
    } catch (error) {
      console.error('Upload error:', error);
      const errorMessage = 'Beklenmeyen bir hata oluştu';
      if (gymId) {
        toast.error(errorMessage);
      }
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setUploading(false);
    }
  };

  const handleCoverChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setCoverError(null);

    if (file) {
      if (!validateFile(file)) {
        setCoverError(
          'Geçersiz dosya. JPEG, PNG veya WebP formatında, en fazla 5MB olmalıdır.'
        );
        return;
      }

      // Preview'ı ayarla
      const previewUrl = URL.createObjectURL(file);
      setCoverPreview(previewUrl);

      // Upload işlemini yap
      await handleFileUpload(file);
    } else {
      setCoverPreview(null);
    }
  };

  const handleCoverRemove = () => {
    setCoverError(null);
    setCoverPreview(null);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex justify-center">
          {/* Cover Image Upload */}
          <div className="w-full max-w-md space-y-4">
            <Label htmlFor="cover" className="block text-center">
              Kapak Görseli
            </Label>
            <div className="flex flex-col items-center space-y-4">
              {coverPreview ? (
                <div className="relative">
                  <Image
                    src={coverPreview}
                    alt="Kapak önizleme"
                    width={200}
                    height={120}
                    className="rounded-lg border object-cover"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                    onClick={handleCoverRemove}
                    disabled={disabled || uploading}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ) : showCurrentImages && currentCoverUrl ? (
                <div className="relative">
                  <Image
                    src={currentCoverUrl}
                    alt="Mevcut kapak"
                    width={200}
                    height={120}
                    className="rounded-lg border object-cover"
                  />
                  <div className="absolute -top-2 -right-2">
                    <div className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900 dark:text-green-200">
                      Mevcut
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex h-32 w-48 items-center justify-center rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
                  <Camera className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <Input
                id="cover"
                type="file"
                accept="image/*"
                onChange={handleCoverChange}
                className="w-full"
                disabled={disabled || uploading}
              />
              {coverError && (
                <p className="text-sm text-red-600 dark:text-red-400">
                  {coverError}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="mt-4 text-center">
          <p className="text-muted-foreground text-sm">
            JPEG, PNG veya WebP formatında, en fazla 5MB
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

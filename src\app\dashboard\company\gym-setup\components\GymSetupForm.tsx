'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import {
  Building2,
  MapPin,
  Clock,
  Star,
  Loader2,
  Plus,
  X,
  Eye,
} from 'lucide-react';
import { CityDistrictSelector } from '@/components/ui/city-district-selector';
import { toast } from 'sonner';
import { FEATURE_GROUPS, GYM_TYPES } from '@/lib/constants';
import { createGym } from '@/lib/actions/dashboard/company/gym-actions';
import { getManagerCompany } from '@/lib/actions/all-actions';
import { Companies } from '@/types/database/tables';
import { useEffect } from 'react';
import { resolveCityName } from '@/lib/utils/display-helpers';
import Image from 'next/image';

export function GymSetupForm() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [company, setCompany] = useState<Companies | null>(null);
  const [, setLoadingCompany] = useState(true);

  const steps = [
    {
      key: 'basic',
      title: 'Temel Bilgiler',
      description: 'Ad, tür ve açıklama',
      icon: <Building2 className="h-4 w-4" />,
    },
    {
      key: 'location',
      title: 'Konum',
      description: 'Şehir, ilçe ve adres',
      icon: <MapPin className="h-4 w-4" />,
    },
    {
      key: 'hours',
      title: 'Saatler',
      description: 'Çalışma ve randevu saatleri',
      icon: <Clock className="h-4 w-4" />,
    },
    {
      key: 'features',
      title: 'Özellikler',
      description: 'Salon özellikleri',
      icon: <Star className="h-4 w-4" />,
    },
    {
      key: 'review',
      title: 'Önizleme',
      description: 'Kontrol ve oluştur',
      icon: <Building2 className="h-4 w-4" />,
    },
  ] as const;
  type StepKey = (typeof steps)[number]['key'];
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    gym_type: '',
    description: '',
    address: '',
    gym_phone: '',
    company_id: '',
    opening_time: '',
    closing_time: '',
    max_capacity: '',
    cover_image_url: '',
  });

  const [features, setFeatures] = useState<string[]>([]);
  const [city, setCity] = useState('');
  const [district, setDistrict] = useState('');
  const [timeSlots, setTimeSlots] = useState<string[]>([]);
  const [newTimeSlot, setNewTimeSlot] = useState('');
  const [mobileTab, setMobileTab] = useState<'form' | 'preview'>('form');

  const currentStep: StepKey = steps[currentStepIndex].key;
  const isLastStep = currentStepIndex === steps.length - 1;
  const progressValue = ((currentStepIndex + 1) / steps.length) * 100;

  // Load company on mount (tek şirket mantığı)
  useEffect(() => {
    async function loadCompany() {
      try {
        const response = await getManagerCompany();
        if (response.success && response.data) {
          setCompany(response.data);
          // Auto-set company ID
          setFormData(prev => ({ ...prev, company_id: response.data!.id }));
        }
      } catch (error) {
        console.error('Failed to load company:', error);
        toast.error('Şirket bilgileri yüklenirken hata oluştu');
      } finally {
        setLoadingCompany(false);
      }
    }

    loadCompany();
  }, []);

  // No-op for now (reserved for potential mount-time effects)

  // Handle form input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Handle feature toggle
  const handleFeatureToggle = (feature: string) => {
    setFeatures(prev =>
      prev.includes(feature)
        ? prev.filter(f => f !== feature)
        : [...prev, feature]
    );
  };

  // Handle time slot addition
  const handleAddTimeSlot = () => {
    if (!newTimeSlot) {
      toast.error('Randevu saati seçiniz.');
      return;
    }

    // Aynı saat zaten var mı kontrol et
    if (timeSlots.includes(newTimeSlot)) {
      toast.error('Bu saat zaten eklenmiş.');
      return;
    }

    setTimeSlots(prev => [...prev, newTimeSlot].sort());
    setNewTimeSlot('');
  };

  // Round current time to nearest 5 minutes and return HH:MM
  const getNearestTimeString = () => {
    const now = new Date();
    const minutes = now.getMinutes();
    const rounded = Math.round(minutes / 5) * 5;
    if (rounded === 60) {
      now.setHours(now.getHours() + 1);
      now.setMinutes(0);
    } else {
      now.setMinutes(rounded);
    }
    const hh = String(now.getHours()).padStart(2, '0');
    const mm = String(now.getMinutes()).padStart(2, '0');
    return `${hh}:${mm}`;
  };

  // Handle time slot removal
  const handleRemoveTimeSlot = (slot: string) => {
    setTimeSlots(prev => prev.filter(s => s !== slot));
  };

  // Per-step validation
  const validateStep = (step: StepKey): boolean => {
    if (step === 'basic') {
      if (!formData.gym_type) {
        toast.error('Salon türü seçiniz.');
        return false;
      }
      // name is optional if company name exists
      if (!formData.name.trim() && !company?.name) {
        toast.error('Salon adı gereklidir veya şirket adı tanımlanmalıdır.');
        return false;
      }
      return true;
    }
    if (step === 'location') {
      if (!city) {
        toast.error('Şehir seçiniz.');
        return false;
      }
      if (!district) {
        toast.error('İlçe seçiniz.');
        return false;
      }
      if (!formData.address.trim()) {
        toast.error('Adres bilgisi gereklidir.');
        return false;
      }
      return true;
    }
    if (step === 'hours') {
      // no hard requirements, allow empty hours
      return true;
    }
    if (step === 'features') {
      // optional
      return true;
    }
    if (step === 'review') {
      // final check mirrors handleSubmit validations
      if (!formData.gym_type) return false;
      if (!city || !district) return false;
      if (!formData.address.trim()) return false;
      if (!formData.name.trim() && !company?.name) return false;
      return true;
    }
    return true;
  };

  const goNext = () => {
    const step = steps[currentStepIndex].key;
    if (!validateStep(step)) return;
    setCurrentStepIndex(i => Math.min(i + 1, steps.length - 1));
  };

  const goBack = () => {
    setCurrentStepIndex(i => Math.max(i - 1, 0));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateStep('review')) {
      // jump to first invalid step for better UX
      const order: StepKey[] = [
        'basic',
        'location',
        'hours',
        'features',
        'review',
      ];
      for (let i = 0; i < order.length; i++) {
        if (!validateStep(order[i])) {
          setCurrentStepIndex(order.findIndex(s => s === order[i]));
          break;
        }
      }
      return;
    }
    setIsSubmitting(true);

    try {
      // Create FormData object
      const submitData = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        if (value) {
          submitData.append(key, value);
        } else if (key === 'name' && !value && company?.name) {
          // Salon adı boşsa şirket adını kullan
          submitData.append(key, company.name);
        } else if (key === 'gym_phone' && !value && company?.phone) {
          // Telefon numarası boşsa şirket telefonunu kullan
          submitData.append(key, company.phone);
        }
      });

      submitData.append('city', city);
      submitData.append('district', district);
      submitData.append('features', JSON.stringify(features));
      submitData.append('time_slots', JSON.stringify(timeSlots));

      const result = await createGym(submitData);

      if (result?.success) {
        toast.success('Salon başarıyla oluşturuldu!');
        router.push('/dashboard/company/gym-setup?success=true');
      } else if (result?.error) {
        toast.error(result.error);
      }
    } catch (error) {
      toast.error('Beklenmeyen bir hata oluştu.');
    } finally {
      setIsSubmitting(false);
    }
  };

  function PreviewPanel() {
    return (
      <Card className="sticky top-6">
        <CardHeader className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-base font-semibold tracking-tight">
            <Eye className="h-4 w-4" aria-hidden="true" /> Canlı Önizleme
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-5">
          <div className="space-y-1 text-left">
            <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
              Salon Adı
            </div>
            <div className="text-foreground text-sm font-semibold">
              {formData.name || company?.name || '—'}
            </div>
          </div>
          {formData.cover_image_url && (
            <div className="space-y-1 text-left">
              <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                Kapak Görseli
              </div>
              <div className="relative h-32 w-full overflow-hidden rounded-lg border">
                <Image
                  src={formData.cover_image_url}
                  alt="Salon kapak görseli önizlemesi"
                  className="h-full w-full object-cover"
                  onError={e => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML =
                        '<div class="flex h-full w-full items-center justify-center bg-muted text-muted-foreground text-sm">Görsel yüklenemedi</div>';
                    }
                  }}
                />
              </div>
            </div>
          )}
          <div className="space-y-3">
            <div className="text-left">
              <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                Tür
              </div>
              <div className="text-foreground text-sm font-semibold">
                {GYM_TYPES.find(t => t.value === formData.gym_type)?.label ||
                  '—'}
              </div>
            </div>
            <div className="text-left">
              <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                Kapasite
              </div>
              <div className="text-foreground text-sm font-semibold">
                {formData.max_capacity || '—'}
              </div>
            </div>
          </div>
          <div className="space-y-1 text-left">
            <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
              Adres
            </div>
            <div className="text-foreground text-sm font-semibold whitespace-pre-line">
              {(city && district
                ? `${district}\n${resolveCityName(city)}`
                : '—') + (formData.address ? `\n${formData.address}` : '')}
            </div>
          </div>
          <div className="space-y-2 text-left">
            <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
              Saatler
            </div>
            <div className="text-foreground text-sm font-semibold">
              {formData.opening_time || formData.closing_time
                ? `${formData.opening_time || '—'} - ${formData.closing_time || '—'}`
                : 'Henüz çalışma saatleri eklenmedi'}
            </div>
            <div className="mt-1">
              {timeSlots.length > 0 ? (
                <div className="flex flex-wrap gap-1.5">
                  {timeSlots.slice(0, 6).map((s, i) => (
                    <Badge key={i} variant="secondary" className="px-2 py-0.5">
                      {s}
                    </Badge>
                  ))}
                  {timeSlots.length > 6 && (
                    <Badge variant="outline">+{timeSlots.length - 6}</Badge>
                  )}
                </div>
              ) : (
                <div className="text-foreground/70 text-xs">
                  Henüz randevu saati eklenmedi
                </div>
              )}
            </div>
          </div>
          <div className="text-left">
            <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
              Özellikler
            </div>
            {features.length > 0 ? (
              <div className="mt-1 flex flex-wrap gap-1.5">
                {features.slice(0, 8).map((f, i) => (
                  <Badge key={i} variant="secondary" className="px-2 py-0.5">
                    {f}
                  </Badge>
                ))}
                {features.length > 8 && (
                  <Badge variant="outline">+{features.length - 8}</Badge>
                )}
              </div>
            ) : (
              <div className="text-foreground/70 text-xs">
                Henüz özellik eklenmedi
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div
      className="container mx-auto max-w-6xl p-6"
      onTouchStart={e =>
        ((e.currentTarget as HTMLElement).dataset.touchStart = String(
          e.touches[0].clientX
        ))
      }
      onTouchEnd={e => {
        const startX = Number(
          (e.currentTarget as HTMLElement).dataset.touchStart || 0
        );
        const endX = e.changedTouches[0].clientX;
        const delta = endX - startX;
        if (Math.abs(delta) > 60) {
          // swipe left shows preview, swipe right shows form
          setMobileTab(delta < 0 ? 'preview' : 'form');
        }
      }}
    >
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Salon Kurulumu</h1>
      </div>

      {/* Mobile Tabs */}
      <div className="mb-3 flex gap-2 md:hidden">
        <button
          type="button"
          aria-pressed={mobileTab === 'form'}
          onClick={() => setMobileTab('form')}
          className={cn(
            'flex-1 rounded-full border px-4 py-2 text-sm font-medium transition-colors',
            mobileTab === 'form'
              ? 'border-primary bg-primary/10 text-primary'
              : 'border-muted text-muted-foreground'
          )}
        >
          Form
        </button>
        <button
          type="button"
          aria-pressed={mobileTab === 'preview'}
          onClick={() => setMobileTab('preview')}
          className={cn(
            'flex-1 rounded-full border px-4 py-2 text-sm font-medium transition-colors',
            mobileTab === 'preview'
              ? 'border-primary bg-primary/10 text-primary'
              : 'border-muted text-muted-foreground'
          )}
        >
          Önizleme
        </button>
      </div>

      {/* Stepper */}
      <div className="mb-4">
        <div className="flex items-center justify-between">
          {steps.map((s, idx) => {
            const isActive = idx === currentStepIndex;
            const isDone = idx < currentStepIndex;
            return (
              <div key={s.key} className="flex min-w-0 flex-1 items-center">
                <div
                  className={cn(
                    'flex items-center gap-2 rounded-full border px-3 py-1.5 text-sm transition-all',
                    isActive &&
                      'border-primary bg-primary/10 text-primary shadow-glow scale-[1.02]',
                    isDone &&
                      'border-green-500 bg-green-500/10 text-green-700 dark:text-green-400',
                    !isActive && !isDone && 'border-muted text-muted-foreground'
                  )}
                >
                  <div
                    className={cn(
                      'flex h-6 w-6 items-center justify-center rounded-full',
                      isDone
                        ? 'bg-green-500 text-white'
                        : isActive
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                    )}
                  >
                    {s.icon}
                  </div>
                  <span className="truncate font-medium">{s.title}</span>
                </div>
                {idx < steps.length - 1 && (
                  <div className="bg-border mx-2 hidden h-1 flex-1 rounded md:block" />
                )}
              </div>
            );
          })}
        </div>
        <div className="mt-2">
          <Progress value={progressValue} />
        </div>
      </div>

      <form
        onSubmit={handleSubmit}
        className="grid grid-cols-1 gap-4 md:grid-cols-3"
      >
        {/* Left: Steps content */}
        <div
          className={cn(
            `${currentStep === 'review' ? 'md:col-span-3' : 'md:col-span-2'} space-y-4`,
            mobileTab === 'preview' ? 'hidden md:block' : ''
          )}
        >
          {currentStep === 'basic' && (
            <Card>
              <CardContent className="space-y-5">
                <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
                  <div className="space-y-3">
                    <Label htmlFor="name" className="font-semibold">
                      Salon Adı
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={e => handleInputChange('name', e.target.value)}
                      placeholder={
                        company?.name
                          ? `Boş bırakılırsa "${company.name}" kullanılacak`
                          : 'Örn: FitLife Spor Salonu'
                      }
                    />
                    {!formData.name && company?.name && null}
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="gym_type" className="font-semibold">
                      Salon Türü *
                    </Label>
                    <Select
                      name="gym_type"
                      value={formData.gym_type}
                      onValueChange={value =>
                        handleInputChange('gym_type', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Salon türü seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {GYM_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="description" className="font-semibold">
                    Açıklama
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={e =>
                      handleInputChange('description', e.target.value)
                    }
                    placeholder="Kısa açıklama..."
                    rows={3}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="cover_image_url" className="font-semibold">
                    Kapak Görseli URL&apos;i
                  </Label>
                  <Input
                    id="cover_image_url"
                    name="cover_image_url"
                    type="url"
                    value={formData.cover_image_url}
                    onChange={e =>
                      handleInputChange('cover_image_url', e.target.value)
                    }
                    placeholder="https://example.com/gym-cover.jpg"
                  />
                  <p className="text-muted-foreground text-sm">
                    Salonunuzun kapak görselinin URL&apos;ini girin (isteğe
                    bağlı)
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
                  <div className="space-y-3">
                    <Label htmlFor="max_capacity" className="font-semibold">
                      Maksimum Kapasite
                    </Label>
                    <Input
                      id="max_capacity"
                      name="max_capacity"
                      type="number"
                      min="1"
                      value={formData.max_capacity}
                      onChange={e =>
                        handleInputChange('max_capacity', e.target.value)
                      }
                      placeholder="Örn: 100"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="gym_phone" className="font-semibold">
                      Telefon
                    </Label>
                    <Input
                      id="gym_phone"
                      name="gym_phone"
                      type="tel"
                      value={formData.gym_phone}
                      onChange={e =>
                        handleInputChange('gym_phone', e.target.value)
                      }
                      placeholder={
                        company?.phone
                          ? `Boş bırakılırsa "${company.phone}" kullanılacak`
                          : '0555 123 45 67'
                      }
                    />
                    {!formData.gym_phone && company?.phone && null}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {currentStep === 'location' && (
            <Card>
              <CardContent className="space-y-5">
                <div className="space-y-3">
                  <Label className="font-semibold">Şehir ve İlçe *</Label>
                  <CityDistrictSelector
                    cityValue={city}
                    districtValue={district}
                    onCityChange={setCity}
                    onDistrictChange={setDistrict}
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="address" className="font-semibold">
                    Adres *
                  </Label>
                  <Textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={e => handleInputChange('address', e.target.value)}
                    placeholder="Tam adres..."
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {currentStep === 'hours' && (
            <Card>
              <CardContent className="space-y-5">
                <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
                  <div className="space-y-3">
                    <Label htmlFor="opening_time" className="font-semibold">
                      Açılış Saati
                    </Label>
                    <Input
                      id="opening_time"
                      name="opening_time"
                      type="time"
                      value={formData.opening_time}
                      onChange={e =>
                        handleInputChange('opening_time', e.target.value)
                      }
                      onFocus={() => {
                        if (!formData.opening_time) {
                          const t = getNearestTimeString();
                          handleInputChange('opening_time', t);
                        }
                      }}
                      placeholder="--:--"
                      className="placeholder:opacity-60"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="closing_time" className="font-semibold">
                      Kapanış Saati
                    </Label>
                    <Input
                      id="closing_time"
                      name="closing_time"
                      type="time"
                      value={formData.closing_time}
                      onChange={e =>
                        handleInputChange('closing_time', e.target.value)
                      }
                      onFocus={() => {
                        if (!formData.closing_time) {
                          const t = getNearestTimeString();
                          handleInputChange('closing_time', t);
                        }
                      }}
                      placeholder="--:--"
                      className="placeholder:opacity-60"
                    />
                  </div>
                </div>

                <div className="space-y-5">
                  <div>
                    <Label className="font-semibold">Randevu Saatleri</Label>
                    <p className="text-foreground/70 mt-1 text-sm">
                      Salonunuzda randevu alınabilecek saatleri belirleyin
                    </p>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Input
                        type="time"
                        value={newTimeSlot}
                        onChange={e => setNewTimeSlot(e.target.value)}
                        onFocus={() => {
                          if (!newTimeSlot)
                            setNewTimeSlot(getNearestTimeString());
                        }}
                        className="flex-1 placeholder:opacity-60"
                        placeholder="Saat seçin"
                      />
                      <Button
                        type="button"
                        onClick={handleAddTimeSlot}
                        variant="outline"
                        size="icon"
                        className="shrink-0"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    {timeSlots.length > 0 ? (
                      <div className="space-y-3">
                        <Label className="text-sm font-semibold">
                          Eklenen Randevu Saatleri:
                        </Label>
                        <div className="flex flex-wrap gap-2">
                          {timeSlots.map((slot, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="flex items-center gap-1.5 px-3 py-1"
                            >
                              <Clock className="h-3 w-3" />
                              {slot}
                              <button
                                type="button"
                                onClick={() => handleRemoveTimeSlot(slot)}
                                className="hover:bg-destructive/10 hover:text-destructive ml-1 rounded transition-colors"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="bg-muted/50 text-foreground/70 rounded-lg p-3 text-sm">
                        Henüz randevu saati eklenmemiş. Yukarıdaki alandan saat
                        ekleyebilirsiniz.
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {currentStep === 'features' && (
            <Card>
              <CardContent className="space-y-6">
                {Object.entries(FEATURE_GROUPS).map(([groupKey, group]) => (
                  <div key={groupKey} className="space-y-3">
                    <h4 className="text-sm font-semibold">{group.label}</h4>
                    <div className="grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4">
                      {group.features.map(feature => (
                        <div
                          key={feature}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={feature}
                            checked={features.includes(feature)}
                            onCheckedChange={() => handleFeatureToggle(feature)}
                          />
                          <Label
                            htmlFor={feature}
                            className="cursor-pointer text-sm font-normal"
                          >
                            {feature}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {currentStep === 'review' && (
            <Card>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-1">
                    <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                      Salon Adı
                    </div>
                    <div className="font-semibold">
                      {formData.name || company?.name}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                      Tür
                    </div>
                    <div className="font-semibold">
                      {
                        GYM_TYPES.find(t => t.value === formData.gym_type)
                          ?.label
                      }
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                      Telefon
                    </div>
                    <div className="font-semibold">
                      {formData.gym_phone || company?.phone || '—'}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                      Kapasite
                    </div>
                    <div className="font-semibold">
                      {formData.max_capacity || '—'}
                    </div>
                  </div>
                  <div className="space-y-1 md:col-span-2">
                    <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                      Adres
                    </div>
                    <div className="font-semibold whitespace-pre-line">{`${district}\n${resolveCityName(city) || city}\n${formData.address}`}</div>
                  </div>
                  {formData.cover_image_url && (
                    <div className="space-y-1 md:col-span-2">
                      <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                        Kapak Görseli
                      </div>
                      <div className="relative h-32 w-full overflow-hidden rounded-lg border">
                        <Image
                          src={formData.cover_image_url}
                          alt="Salon kapak görseli"
                          className="h-full w-full object-cover"
                          onError={e => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const parent = target.parentElement;
                            if (parent) {
                              parent.innerHTML =
                                '<div class="flex h-full w-full items-center justify-center bg-muted text-muted-foreground text-sm">Görsel yüklenemedi</div>';
                            }
                          }}
                        />
                      </div>
                    </div>
                  )}
                  <div className="space-y-1">
                    <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                      Çalışma Saatleri
                    </div>
                    <div className="font-semibold">
                      {formData.opening_time || '—'} -{' '}
                      {formData.closing_time || '—'}
                    </div>
                  </div>
                  <div className="space-y-1 md:col-span-2">
                    <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                      Randevu Saatleri
                    </div>
                    {timeSlots.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {timeSlots.map((s, i) => (
                          <Badge
                            key={i}
                            variant="secondary"
                            className="px-2 py-0.5"
                          >
                            {s}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <div className="text-foreground/70 text-xs">
                        Henüz randevu saati eklenmedi
                      </div>
                    )}
                  </div>
                  <div className="space-y-1 md:col-span-2">
                    <div className="text-foreground/70 text-xs font-semibold tracking-wide uppercase">
                      Özellikler
                    </div>
                    {features.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {features.map((f, i) => (
                          <Badge
                            key={i}
                            variant="secondary"
                            className="px-2 py-0.5"
                          >
                            {f}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <div className="text-foreground/70 text-xs">
                        Henüz özellik eklenmedi
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Navigation */}
          <div className="flex flex-col-reverse items-stretch gap-3 pb-20 sm:flex-row sm:justify-between">
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isSubmitting}
              >
                İptal
              </Button>
              {currentStepIndex > 0 && (
                <Button
                  type="button"
                  variant="secondary"
                  onClick={goBack}
                  disabled={isSubmitting}
                >
                  Geri
                </Button>
              )}
            </div>
            <div className="hidden gap-3 sm:flex">
              {!isLastStep && (
                <Button
                  type="button"
                  onClick={goNext}
                  disabled={isSubmitting}
                  className="min-w-28 bg-[oklch(0.7049_0.1867_47.6044)] hover:bg-[oklch(0.7049_0.1867_47.6044_/_0.9)]"
                >
                  İleri
                </Button>
              )}
              {isLastStep && (
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="min-w-32 bg-[oklch(0.7049_0.1867_47.6044)] hover:bg-[oklch(0.7049_0.1867_47.6044_/_0.9)]"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Oluşturuluyor...
                    </>
                  ) : (
                    'Salon Oluştur'
                  )}
                </Button>
              )}
            </div>
            {/* Floating next on mobile */}
            {!isLastStep && (
              <Button
                type="button"
                onClick={goNext}
                disabled={isSubmitting}
                className="shadow-glow fixed right-6 bottom-6 z-20 h-12 min-w-32 rounded-full bg-[oklch(0.7049_0.1867_47.6044)] hover:bg-[oklch(0.7049_0.1867_47.6044_/_0.9)] sm:hidden"
              >
                İleri
              </Button>
            )}
            {isLastStep && (
              <Button
                type="submit"
                disabled={isSubmitting}
                className="shadow-glow fixed right-6 bottom-6 z-20 h-12 min-w-36 rounded-full bg-[oklch(0.7049_0.1867_47.6044)] hover:bg-[oklch(0.7049_0.1867_47.6044_/_0.9)] sm:hidden"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Oluşturuluyor...
                  </>
                ) : (
                  'Salon Oluştur'
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Right: Live preview (hidden on review step to avoid duplication) */}
        {currentStep !== 'review' && (
          <div
            className={cn(
              'md:col-span-1',
              mobileTab === 'preview' ? '' : 'hidden md:block'
            )}
          >
            <PreviewPanel />
          </div>
        )}
      </form>
    </div>
  );
}
